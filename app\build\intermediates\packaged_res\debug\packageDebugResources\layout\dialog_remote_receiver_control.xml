<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/visualization_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#EDEDED">

        <!-- 投屏窗口容器可视化组件 -->
        <com.example.castapp.ui.view.WindowContainerVisualizationView
            android:id="@+id/window_visualization_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />

        <!-- 标题栏 (浮动在可视化层之上) -->
        <LinearLayout
            android:id="@+id/title_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="top"
            android:gravity="center_vertical">

                <TextView
                android:id="@+id/dialog_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="远程接收端控制"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginStart="8dp"
                android:textColor="#40000000" />

            <ImageButton
                android:id="@+id/close_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_close"
                android:contentDescription="关闭"
                android:scaleType="centerInside"
                android:alpha="0.7"
                app:tint="#40000000" />

        </LinearLayout>

        <!-- 右下角控制按钮区域 (浮动在可视化层之上) -->
        <LinearLayout
            android:id="@+id/control_buttons_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_gravity="bottom|end"
            android:layout_marginBottom="2dp"
            android:layout_marginEnd="4dp"
            android:background="#40000000"
            android:padding="2dp">

            <!-- 清屏按钮 -->
            <Button
                android:id="@+id/clear_screen_button"
                android:layout_width="35dp"
                android:layout_height="40dp"
                android:background="#00FFFFFF"
                android:drawableTop="@drawable/ic_clear"
                android:text="清屏"
                android:drawablePadding="-4dp"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"
                android:textSize="10dp"
                android:textColor="#FFFFFF" />

            <!-- 保存按钮 -->
            <Button
                android:id="@+id/save_button"
                android:layout_width="35dp"
                android:layout_height="40dp"
                android:background="#00FFFFFF"
                android:drawableTop="@drawable/ic_save"
                android:text="保存"
                android:drawablePadding="-4dp"
                android:layout_marginBottom="8dp"
                android:textSize="10dp"
                android:textColor="#FFFFFF" />

            <!-- 导播按钮 -->
            <Button
                android:id="@+id/broadcast_button"
                android:layout_width="35dp"
                android:layout_height="40dp"
                android:background="#00FFFFFF"
                android:drawableTop="@drawable/ic_director"
                android:text="导播"
                android:drawablePadding="-4dp"
                android:layout_marginBottom="8dp"
                android:textSize="10dp"
                android:textColor="#FFFFFF" />

            <!-- 窗口按钮 -->
            <Button
                android:id="@+id/window_button"
                android:layout_width="35dp"
                android:layout_height="40dp"
                android:background="#00FFFFFF"
                android:drawableTop="@drawable/ic_window_settings"
                android:text="窗口"
                android:drawablePadding="-4dp"
                android:layout_marginBottom="8dp"
                android:textSize="10dp"
                android:textColor="#FFFFFF" />

            <!-- 层级按钮 -->
            <Button
                android:id="@+id/layer_button"
                android:layout_width="35dp"
                android:layout_height="40dp"
                android:background="#00FFFFFF"
                android:drawableTop="@drawable/ic_layer"
                android:text="层级"
                android:drawablePadding="-4dp"
                android:layout_marginBottom="8dp"
                android:textSize="10dp"
                android:textColor="#FFFFFF" />

            <!-- 接收按钮 -->
            <Button
                android:id="@+id/receive_button"
                android:layout_width="35dp"
                android:layout_height="40dp"
                android:background="#00FFFFFF"
                android:drawableTop="@drawable/ic_cast"
                android:text="接收"
                android:drawablePadding="-4dp"
                android:layout_marginBottom="8dp"
                android:textSize="10dp"
                android:textColor="#FFFFFF" />

            <!-- 更新按钮 -->
            <Button
                android:id="@+id/update_button"
                android:layout_width="35dp"
                android:layout_height="40dp"
                android:background="#00FFFFFF"
                android:drawableTop="@drawable/ic_updated"
                android:text="更新"
                android:drawablePadding="-4dp"
                android:layout_marginBottom="8dp"
                android:textSize="10dp"
                android:textColor="#FFFFFF" />

            <!-- 同步按钮 -->
            <Button
                android:id="@+id/sync_button"
                android:layout_width="35dp"
                android:layout_height="40dp"
                android:background="#00FFFFFF"
                android:drawableTop="@drawable/ic_refresh"
                android:text="同步"
                android:drawablePadding="-4dp"
                android:layout_marginBottom="8dp"
                android:textSize="10dp"
                android:textColor="#FFFFFF" />

        </LinearLayout>

</FrameLayout>
