遥控端日志：🔄 更新按钮被点击 - 检查连接状态: true
🔄 更新按钮被点击 - 执行窗口信息更新并请求文字内容
🔄 创建临时更新处理器（FULL模式）
🔄 临时更新处理器创建完成
【窗口管理】注册窗口管理对话框: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a
[WS] 发送控制消息: window_manager_request
【窗口管理】发送窗口信息请求到: vbvgb
【窗口管理】使用连接ID: remote_control_1756126267931 (而非receiver.id: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a)
🔄 窗口信息更新请求已发送到: vbvgb
[WS] 收到控制消息: window_manager_response
收到远程设备消息: window_manager_response
处理接收端设备消息: window_manager_response from vbvgb
【窗口管理】收到窗口管理响应: vbvgb
【窗口管理】通知窗口管理对话框更新: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a
【远程窗口管理】收到窗口信息响应，窗口数量: 2
【编辑状态】获取编辑状态: text_00701e4b-cf92-48f0-a87b-ac71b890578e, 内存状态: null
【编辑状态】从SharedPreferences加载: text_00701e4b-cf92-48f0-a87b-ac71b890578e -> false
【编辑状态】返回持久化状态: text_00701e4b-cf92-48f0-a87b-ac71b890578e -> false
【编辑状态】获取编辑状态: text_c3428204-bd40-4afe-a58a-1c42423af262, 内存状态: false
【编辑状态】返回内存状态: text_c3428204-bd40-4afe-a58a-1c42423af262 -> false
【远程窗口管理】更新窗口列表，数量: 2
【远程窗口管理】文字窗口 0: 本地设备:0 - 文本内容, 遥控端编辑状态: false
【远程窗口管理】文字窗口 1: 本地设备:0 - 文本内容, 遥控端编辑状态: false
【远程窗口管理】Fragment未附加到Context，跳过缓存保存
🔄 临时更新处理器收到窗口信息更新回调: 2 个窗口
【窗口可视化】更新窗口可视化数据: 2 个窗口
【统一配置管理器】从接收端更新窗口配置: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a, 2 个窗口
【文本格式管理器】未找到富文本格式: ID=text_00701e4b-cf92-48f0-a87b-ac71b890578e
【文本格式管理器】未找到保存的格式: ID=text_00701e4b-cf92-48f0-a87b-ac71b890578e
【统一配置管理器】📝 未找到保存的文字格式: text_00701e4b-cf92-48f0-a87b-ac71b890578e
【文本格式管理器】未找到保存的扩展格式: ID=text_00701e4b-cf92-48f0-a87b-ac71b890578e
【统一配置管理器】更新窗口配置: text_00701e4b-cf92-48f0-a87b-ac71b890578e
【文本格式管理器】未找到富文本格式: ID=text_c3428204-bd40-4afe-a58a-1c42423af262
【文本格式管理器】未找到保存的格式: ID=text_c3428204-bd40-4afe-a58a-1c42423af262
【统一配置管理器】📝 未找到保存的文字格式: text_c3428204-bd40-4afe-a58a-1c42423af262
【文本格式管理器】未找到保存的扩展格式: ID=text_c3428204-bd40-4afe-a58a-1c42423af262
【统一配置管理器】更新窗口配置: text_c3428204-bd40-4afe-a58a-1c42423af262
【统一配置管理器】接收端数据更新完成: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a
【统一配置管理器】已保存配置到本地存储: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a
【遥控端缓存】已保存窗口信息: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a, 2 个窗口
【可视化计算】开始计算 2 个窗口的可视化数据
  🎯 统一缩放：所有窗口数据层保持原始尺寸，View层应用缩放
【可视化位置计算调试】窗口: text_00701e4b-cf92-48f0-a87b-ac71b890578e
  接收端发送的容器位置: (0.0, 0.0)
  是否裁剪: false
  是否文字窗口: true
  旋转角度: 0.0°
  📝 文字窗口：直接使用接收端位置（已包含旋转变换）: (0.0, 0.0)
  远程控制缩放: 0.8483870967741935
  最终可视化位置: (0.0, 0.0)
【尺寸计算】窗口: 本地设备:0 - 文本内容
  基础尺寸: 300×200
  是否裁剪: false
  窗口缩放: 1.0
  🎯 容器尺寸（基于原始窗口）: 300×200
【参数同步】🎯 保持用户调整后的尺寸: text_c3428204-bd40-4afe-a58a-1c42423af262 -> 255x170
【参数同步】📝 更新文字窗口可视化数据: text_c3428204-bd40-4afe-a58a-1c42423af262, 内容=''
  远程控制缩放: 0.848387
  可视化尺寸: 254×169
【窗口可视化】🎯 保留文字格式信息: text_c3428204-bd40-4afe-a58a-1c42423af262
  缩放验证: 300 × 0.848387 = 254
【窗口可视化】🎯 现有文字内容: '默认文字', 行间距: 0.0, 对齐: 17
  接收端容器位置: (0.0, 0.0)
【窗口可视化】🎯 新数据文字内容: '', 行间距: 0.0, 对齐: 17
  🎯 直接使用容器位置: (0.0, 0.0)
  可视化位置: (0, 0)
【窗口可视化】🎯 保留后文字内容: '默认文字', 行间距: 0.0, 对齐: 17
【窗口可视化】更新可视化数据: 1 个窗口，1 个可显示
【窗口可视化】🎯 已保留现有文字格式信息，避免重置
  位置验证: 0.0 × 0.848387 = 0
【可视化数据转换】边框参数传递:
  源数据边框启用: false
  源数据边框颜色: #FFFFEAA7
  源数据边框宽度: 2.0dp
【可视化数据转换】📝 已从统一配置管理器获取文字内容: text_00701e4b-cf92-48f0-a87b-ac71b890578e, 内容=''
【可视化计算】窗口 0: 本地设备:0 - 文本内容
  原始位置: (0.0, 0.0)
  原始尺寸: 300×200
  窗口缩放: 1.0
  可视化位置: (0.0, 0.0)
  可视化尺寸: 254.51613×169.67741
  层级: 1, 旋转: 0.0°
  🎯 统一缩放：所有窗口数据层保持原始尺寸，View层应用缩放
【可视化位置计算调试】窗口: text_c3428204-bd40-4afe-a58a-1c42423af262
  接收端发送的容器位置: (409.6, 746.2)
  是否裁剪: false
  是否文字窗口: true
  旋转角度: 0.0°
  📝 文字窗口：直接使用接收端位置（已包含旋转变换）: (409.6, 746.2)
  远程控制缩放: 0.8483870967741935
  最终可视化位置: (347.49936, 633.06647)
【尺寸计算】窗口: 本地设备:0 - 文本内容
  基础尺寸: 300×200
  是否裁剪: false
  窗口缩放: 1.0
  🎯 容器尺寸（基于原始窗口）: 300×200
  远程控制缩放: 0.848387
  可视化尺寸: 254×169
  缩放验证: 300 × 0.848387 = 254
  接收端容器位置: (409.6, 746.2)
  🎯 直接使用容器位置: (409.6, 746.2)
  可视化位置: (347, 633)
  位置验证: 409.6 × 0.848387 = 347
【可视化数据转换】边框参数传递:
  源数据边框启用: false
  源数据边框颜色: #FFFFEAA7
  源数据边框宽度: 2.0dp
【可视化数据转换】📝 已从统一配置管理器获取文字内容: text_c3428204-bd40-4afe-a58a-1c42423af262, 内容=''
【可视化计算】窗口 1: 本地设备:0 - 文本内容
  原始位置: (409.6, 746.2)
  原始尺寸: 300×200
  窗口缩放: 1.0
  可视化位置: (347.49936, 633.06647)
  可视化尺寸: 254.51613×169.67741
  层级: 2, 旋转: 0.0°
【可视化计算】完成，生成 2 个可视化数据对象
【窗口容器View】移除裁剪: 423af262
【窗口可视化】🎯 新文字窗口，无现有数据可保留: text_00701e4b-cf92-48f0-a87b-ac71b890578e
【窗口可视化】🎯 保留文字格式信息: text_c3428204-bd40-4afe-a58a-1c42423af262
【窗口可视化】🎯 现有文字内容: '默认文字', 行间距: 0.0, 对齐: 17
  🎯 统一圆角半径: 16.0dp (固定) = 44.0px
【窗口可视化】🎯 新数据文字内容: '', 行间距: 0.0, 对齐: 17
【窗口可视化】🎯 保留后文字内容: '默认文字', 行间距: 0.0, 对齐: 17
【窗口可视化】更新可视化数据: 2 个窗口，2 个可显示
【窗口可视化】🎯 已保留现有文字格式信息，避免重置
【窗口容器View】移除裁剪: 423af262
  🎯 统一圆角半径: 16.0dp (固定) = 44.0px
【窗口容器View】设置窗口数据: 423af262
  位置: (347.49936, 633.06647)
  尺寸: 255×170
  裁剪状态: false
  镜像状态: false
  文字内容: 默认文字
【窗口容器View】边框状态:
  边框启用: false
  边框颜色: #FFFFEAA7
  边框宽度: 2.0dp
【遥控端格式解析器】开始解析文字格式: 内容长度=4, 富文本数据=false
【遥控端格式解析器】行间距解析调试: 原始值=0.0, 类型=Float, 解析后=0.0dp
【遥控端格式解析器】对齐解析调试: 原始值=17, 类型=Integer, 解析后=17
【遥控端格式解析器】颜色解析调试: 原始值=-1, 类型=Integer, 解析后=#FFFFFFFF
【遥控端格式解析器】格式解析完成: 富文本=false, 字号=13sp, 行间距=0.0dp
【遥控端格式解析器】详细格式信息: 粗体=false, 斜体=false, 对齐=17, SpannableString=false
【遥控端格式解析器】窗口背景颜色: 启用=false, 颜色=#FFFFFFFF
【参数同步】📝 更新文字窗口可视化数据失败
android.view.ViewRootImpl$CalledFromWrongThreadException: Only the original thread that created a view hierarchy can touch its views.
	at android.view.ViewRootImpl.checkThread(ViewRootImpl.java:10558)
	at android.view.ViewRootImpl.requestLayout(ViewRootImpl.java:2237)
	at android.view.View.requestLayout(View.java:27049)
	at android.view.View.requestLayout(View.java:27049)
	at android.view.View.requestLayout(View.java:27049)
	at android.view.View.requestLayout(View.java:27049)
	at android.view.View.requestLayout(View.java:27049)
	at android.view.ViewGroup.onSetLayoutParams(ViewGroup.java:8086)
	at android.view.View.setLayoutParams(View.java:19216)
	at com.example.castapp.ui.view.WindowVisualizationContainerView.setWindowDataInternal(WindowVisualizationContainerView.kt:81)
	at com.example.castapp.ui.view.WindowVisualizationContainerView.setWindowData(WindowVisualizationContainerView.kt:54)
	at com.example.castapp.ui.view.WindowContainerVisualizationView.updateWindowContainerViews(WindowContainerVisualizationView.kt:278)
	at com.example.castapp.ui.view.WindowContainerVisualizationView.updateVisualizationData(WindowContainerVisualizationView.kt:243)
	at com.example.castapp.ui.dialog.RemoteReceiverControlDialog.updateTextWindowVisualizationData(RemoteReceiverControlDialog.kt:616)
	at com.example.castapp.ui.dialog.RemoteReceiverControlDialog.syncVisualizationParamsFromManager(RemoteReceiverControlDialog.kt:540)
	at com.example.castapp.ui.dialog.RemoteReceiverControlDialog.access$syncVisualizationParamsFromManager(RemoteReceiverControlDialog.kt:29)
	at com.example.castapp.ui.dialog.RemoteReceiverControlDialog$createTemporaryUpdateHandler$1.invoke(RemoteReceiverControlDialog.kt:1118)
	at com.example.castapp.ui.dialog.RemoteReceiverControlDialog$createTemporaryUpdateHandler$1.invoke(RemoteReceiverControlDialog.kt:1109)
	at com.example.castapp.ui.dialog.RemoteWindowManagerDialog.updateWindowList(RemoteWindowManagerDialog.kt:395)
	at com.example.castapp.ui.dialog.RemoteWindowManagerDialog.handleWindowManagerResponse(RemoteWindowManagerDialog.kt:274)
	at com.example.castapp.manager.RemoteReceiverManager.notifyWindowManagerDialogs(RemoteReceiverManager.kt:661)
	at com.example.castapp.manager.RemoteReceiverManager.handleWindowManagerResponse(RemoteReceiverManager.kt:629)
	at com.example.castapp.manager.RemoteReceiverManager.handleReceiverMessage(RemoteReceiverManager.kt:268)
	at com.example.castapp.manager.RemoteReceiverManager.access$handleReceiverMessage(RemoteReceiverManager.kt:27)
	at com.example.castapp.manager.RemoteReceiverManager$connectReceiver$client$1.invoke(RemoteReceiverManager.kt:80)
	at com.example.castapp.manager.RemoteReceiverManager$connectReceiver$client$1.invoke(RemoteReceiverManager.kt:75)
	at com.example.castapp.remote.RemoteSenderWebSocketClient$connect$1.invoke(RemoteSenderWebSocketClient.kt:53)
	at com.example.castapp.remote.RemoteSenderWebSocketClient$connect$1.invoke(RemoteSenderWebSocketClient.kt:42)
	at com.example.castapp.websocket.WebSocketClient.onMessage(WebSocketClient.kt:66)
	at org.java_websocket.client.WebSocketClient.onWebsocketMessage(WebSocketClient.java:661)
	at org.java_websocket.drafts.Draft_6455.processFrameText(Draft_6455.java:986)
	at org.java_websocket.drafts.Draft_6455.processFrame(Draft_6455.java:910)
	at org.java_websocket.WebSocketImpl.decodeFrames(WebSocketImpl.java:397)
	at org.java_websocket.WebSocketImpl.decode(WebSocketImpl.java:229)
	at org.java_websocket.client.WebSocketClient.run(WebSocketClient.java:544)
	at java.lang.Thread.run(Thread.java:1012)
【统一配置管理器】同步可视化参数: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a, 2 个窗口
【统一配置管理器】🎯 窗色同步: 可视化数据中的窗色状态 - 启用=false, 颜色=#FFFFFFFF
【窗口容器View】📝 已应用基本格式: 423af262, 字号=13sp
【窗口容器View】📝 已应用行间距: 0.0dp -> 0.0px
【窗口容器View】📝 已应用文本对齐: 17 (CENTER)
【窗口容器View】📝 已设置窗口背景为透明
【窗口容器View】📝 TextView格式更新完成: 423af262, 富文本=false
【层级修复】更新文字窗口View: text_c3428204-bd40-4afe-a58a-1c42423af262, zOrder=2, 文字内容: 默认文字
【边框同步】边框View为null，可能已被回收
【缩放同步修复】应用接收端缩放状态: scaleFactor=1.0
【窗口容器View】移除裁剪: b890578e
  🎯 统一圆角半径: 16.0dp (固定) = 44.0px
【窗口容器View】设置窗口数据: b890578e
  位置: (0.0, 0.0)
  尺寸: 255×170
  裁剪状态: false
  镜像状态: false
  文字内容: 
【窗口容器View】边框状态:
  边框启用: false
【统一配置管理器】🎯 窗色同步: 统一配置中的窗色状态 - 启用=false, 颜色=#FFFFFFFF
【统一配置管理器】🎯 保留统一配置中的窗色状态（可视化数据为默认值）
【统一配置管理器】📝 同步文字内容: text_00701e4b-cf92-48f0-a87b-ac71b890578e, 内容=''
【统一配置管理器】同步可视化参数: text_00701e4b-cf92-48f0-a87b-ac71b890578e
  边框颜色: #FFFFEAA7
  🎯 位置转换: 可视化位置(0.0, 0.0) -> 接收端位置(0.0, 0.0)
  边框宽度: 2.0dp
  🎯 尺寸转换: 遥控端尺寸(300x200) ÷ 0.8483870967741935 -> 接收端尺寸(353x235)
  缩放: 1.0, 旋转: 0.0°
【统一配置管理器】🎯 窗色同步: 可视化数据中的窗色状态 - 启用=false, 颜色=#FFFFFFFF
【统一配置管理器】🎯 窗色同步: 统一配置中的窗色状态 - 启用=false, 颜色=#FFFFFFFF
【统一配置管理器】🎯 保留统一配置中的窗色状态（可视化数据为默认值）
【统一配置管理器】📝 同步文字内容: text_c3428204-bd40-4afe-a58a-1c42423af262, 内容='默认文字'
【统一配置管理器】同步可视化参数: text_c3428204-bd40-4afe-a58a-1c42423af262
  🎯 位置转换: 可视化位置(347.49936, 633.06647) -> 接收端位置(409.6, 746.2)
  🎯 尺寸转换: 遥控端尺寸(300x200) ÷ 0.8483870967741935 -> 接收端尺寸(353x235)
  缩放: 1.0, 旋转: 0.0°
【参数同步】已同步 2 个窗口的可视化参数
🔄 临时处理器窗口信息更新完成，统一配置管理器已同步
🔄 FULL模式全量更新完成：可视化窗口已更新，窗口信息已缓存到本地，统一参数管理器已初始化
【窗口管理】注销窗口管理对话框: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a
🔄 临时更新处理器已注销
【远程窗口管理】已通知窗口信息更新回调
【远程窗口管理】无activity上下文，跳过UI更新（临时处理器模式）
【文本窗口】字号已设置: 13sp
【字体预设管理器】初始化完成
【字体预设管理器】获取完整字体列表: [Roboto]
【文本窗口】字体已更新: Roboto
【文本窗口】默认字体已设置: Roboto
【文本窗口】默认样式设置完成，字号: 13sp
【文本窗口】默认样式设置完成，字号: 13sp
【文本窗口】非编辑模式格式化文本已更新: 加粗=false, 倾斜=false
【文本窗口】TextWindowView 初始化完成
【窗口容器View】📝 TextWindowView已创建: b890578e
【窗口容器View】📝 TextWindowView已添加到容器: b890578e
【窗口容器View】无法获取父容器，跳过边框创建
【层级修复】准备创建文字窗口View: text_00701e4b-cf92-48f0-a87b-ac71b890578e, zOrder=1, 文字内容: 
【缩放同步修复】新窗口应用接收端缩放状态: scaleFactor=1.0
【层级修复】新建窗口View已添加: text_00701e4b-cf92-48f0-a87b-ac71b890578e
【层级修复】可视化窗口和边框已同步调整到前台: 423af262
【层级修复】调整窗口层级: 423af262, zOrder=2
【层级修复】窗口管理完成，当前子View数量: 2
【层级修复】添加顺序（先添加的在底层）: [423af262(z=2), b890578e(z=1)]
【层级修复】显示层级（zOrder=1是最上层）: [b890578e(z=1), 423af262(z=2)]
【窗口可视化】可视化更新完成: 2 个窗口容器
【层级修复】窗口层级信息:
  层级1: b890578e (索引0)
  层级2: 423af262 (索引1)
📸 只有文字窗口，跳过截图请求
📝 窗口信息更新完成，自动触发文字内容请求
[WS] 发送控制消息: text_content_request
📝 发送文字内容请求到: vbvgb
📝 使用连接ID: remote_control_1756126267931 (而非receiver.id: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a)
📝 自动文字内容请求已发送到: vbvgb
📝 检测到文字窗口，请求文字内容
【窗口容器View】🎨 跳过所有背景填充绘制，保持透明: 423af262
【窗口容器View】📝 文字窗口由TextView控件显示: 423af262
【边框同步】边框View为null，可能已被回收
[WS] 收到控制消息: text_content_response
收到远程设备消息: text_content_response
处理接收端设备消息: text_content_response from vbvgb
📝 收到文字内容响应: vbvgb
📝 通知远程控制对话框显示文字内容: 3c4d4233-8370-44e8-86ce-7069ebd2aa6a
📝 [vbvgb] 收到文字内容响应消息
📝 消息数据: {text_contents=[{connectionId=text_c3428204-bd40-4afe-a58a-1c42423af262, textContent=默认文字, timestamp=1.756126620656E12, isWindowColorEnabled=false, windowBackgroundColor=-1.0}, {connectionId=text_00701e4b-cf92-48f0-a87b-ac71b890578e, textContent=默认文字, timestamp=1.756126620658E12, isWindowColorEnabled=false, windowBackgroundColor=-1.0}], timestamp=1.75612662066E12}
📝 解析到 2 个文字内容数据
📝 文字内容[0]: connectionId=text_c3428204-bd40-4afe-a58a-1c42423af262, 内容=默认文字
📝 文字内容[1]: connectionId=text_00701e4b-cf92-48f0-a87b-ac71b890578e, 内容=默认文字
📝 开始更新文字内容可视化显示
📝 [WindowVisualization] 开始更新文字内容数据，输入数据数量: 2
📝 [WindowVisualization] 解析文字格式成功: text_c3428204-bd40-4afe-a58a-1c42423af262, 内容: 默认文字, 富文本: false
📝 [WindowVisualization] 解析文字格式成功: text_00701e4b-cf92-48f0-a87b-ac71b890578e, 内容: 默认文字, 富文本: false
【窗口可视化】格式数据传递调试: connectionId=text_00701e4b-cf92-48f0-a87b-ac71b890578e
【窗口可视化】原始行间距: null, 类型: null
【窗口可视化】原始对齐: null, 类型: null
【窗口可视化】🎯 格式更新完成: connectionId=text_00701e4b-cf92-48f0-a87b-ac71b890578e
【窗口可视化】🎯 更新后行间距: 0.0, 对齐: 17
【窗口可视化】格式数据传递调试: connectionId=text_c3428204-bd40-4afe-a58a-1c42423af262
【窗口可视化】原始行间距: null, 类型: null
【窗口可视化】原始对齐: null, 类型: null
【窗口可视化】🎯 格式更新完成: connectionId=text_c3428204-bd40-4afe-a58a-1c42423af262
【窗口可视化】🎯 更新后行间距: 0.0, 对齐: 17
【窗口容器View】移除裁剪: 423af262
  🎯 统一圆角半径: 16.0dp (固定) = 44.0px
【窗口容器View】设置窗口数据: 423af262
  位置: (347.49936, 633.06647)
  尺寸: 255×170
  裁剪状态: false
  镜像状态: false
  文字内容: 默认文字
【窗口容器View】边框状态:
  边框启用: false
  边框颜色: #FFFFEAA7
  边框宽度: 2.0dp
【遥控端格式解析器】开始解析文字格式: 内容长度=4, 富文本数据=false
【遥控端格式解析器】行间距解析调试: 原始值=0.0, 类型=Float, 解析后=0.0dp
【遥控端格式解析器】对齐解析调试: 原始值=17, 类型=Integer, 解析后=17
【遥控端格式解析器】颜色解析调试: 原始值=-1, 类型=Integer, 解析后=#FFFFFFFF
【遥控端格式解析器】格式解析完成: 富文本=false, 字号=13sp, 行间距=0.0dp
【遥控端格式解析器】详细格式信息: 粗体=false, 斜体=false, 对齐=17, SpannableString=false
【遥控端格式解析器】窗口背景颜色: 启用=false, 颜色=#FFFFFFFF
【窗口容器View】📝 已应用基本格式: 423af262, 字号=13sp
【窗口容器View】📝 已应用行间距: 0.0dp -> 0.0px
【窗口容器View】📝 已应用文本对齐: 17 (CENTER)
【窗口容器View】📝 已设置窗口背景为透明
【窗口容器View】📝 TextView格式更新完成: 423af262, 富文本=false
【层级修复】更新文字窗口View: text_c3428204-bd40-4afe-a58a-1c42423af262, zOrder=2, 文字内容: 默认文字
【边框同步】边框View为null，可能已被回收
【缩放同步修复】应用接收端缩放状态: scaleFactor=1.0
【窗口容器View】移除裁剪: b890578e
  🎯 统一圆角半径: 16.0dp (固定) = 44.0px
【窗口容器View】设置窗口数据: b890578e
  位置: (0.0, 0.0)
  尺寸: 255×170
  裁剪状态: false
  镜像状态: false
  文字内容: 
【窗口容器View】边框状态:
  边框启用: false
  边框颜色: #FFFFEAA7
  边框宽度: 2.0dp
【遥控端格式解析器】开始解析文字格式: 内容长度=4, 富文本数据=false
【遥控端格式解析器】行间距解析调试: 原始值=0.0, 类型=Float, 解析后=0.0dp
【遥控端格式解析器】对齐解析调试: 原始值=17, 类型=Integer, 解析后=17
【遥控端格式解析器】颜色解析调试: 原始值=-1, 类型=Integer, 解析后=#FFFFFFFF
【遥控端格式解析器】格式解析完成: 富文本=false, 字号=13sp, 行间距=0.0dp
【遥控端格式解析器】详细格式信息: 粗体=false, 斜体=false, 对齐=17, SpannableString=false
【遥控端格式解析器】窗口背景颜色: 启用=false, 颜色=#FFFFFFFF
【窗口容器View】📝 已应用基本格式: b890578e, 字号=13sp
【窗口容器View】📝 已应用行间距: 0.0dp -> 0.0px
【窗口容器View】📝 已应用文本对齐: 17 (CENTER)
【窗口容器View】📝 已设置窗口背景为透明
【窗口容器View】📝 TextView格式更新完成: b890578e, 富文本=false
【层级修复】更新文字窗口View: text_00701e4b-cf92-48f0-a87b-ac71b890578e, zOrder=1, 文字内容: 默认文字
【边框同步】边框View为null，可能已被回收
【缩放同步修复】应用接收端缩放状态: scaleFactor=1.0
【层级修复】可视化窗口和边框已同步调整到前台: 423af262
【层级修复】调整窗口层级: 423af262, zOrder=2
【层级修复】可视化窗口和边框已同步调整到前台: b890578e
【层级修复】调整窗口层级: b890578e, zOrder=1
【层级修复】窗口管理完成，当前子View数量: 2
【层级修复】添加顺序（先添加的在底层）: [423af262(z=2), b890578e(z=1)]
【层级修复】显示层级（zOrder=1是最上层）: [b890578e(z=1), 423af262(z=2)]
📝 [WindowVisualization] 文字格式数据更新完成: 2 个文字窗口
📝 文字内容可视化更新完成: 2 个文字内容
📝 开始同步遥控端文字窗口数据: 2 个窗口
📝 找到遥控端文字窗口: text_c3428204-bd40-4afe-a58a-1c42423af262
📝 开始应用完整格式数据到遥控端文字窗口: text_c3428204-bd40-4afe-a58a-1c42423af262
📝 基本格式数据: 内容='默认文字', 富文本=false, 加粗=false, 倾斜=false, 字号=13sp
【文本窗口】文本内容已更新，保持现有格式: 默认文字
【文本窗口】非编辑模式格式化文本已更新: 加粗=false, 倾斜=false
【文本窗口】格式已应用到整个文本: 加粗=false, 倾斜=false, 字号=13sp
📝 基本格式已应用: 内容='默认文字', 加粗=false, 倾斜=false, 字号=13sp
📝 formatData中无窗色信息，保持当前窗色设置
📝 完整格式数据应用完成: text_c3428204-bd40-4afe-a58a-1c42423af262
📝 遥控端文字窗口同步完成: text_c3428204-bd40-4afe-a58a-1c42423af262
📝 找到遥控端文字窗口: text_00701e4b-cf92-48f0-a87b-ac71b890578e
📝 开始应用完整格式数据到遥控端文字窗口: text_00701e4b-cf92-48f0-a87b-ac71b890578e
📝 基本格式数据: 内容='默认文字', 富文本=false, 加粗=false, 倾斜=false, 字号=13sp
【文本窗口】文本内容已更新，保持现有格式: 默认文字
【文本窗口】非编辑模式格式化文本已更新: 加粗=false, 倾斜=false
【文本窗口】格式已应用到整个文本: 加粗=false, 倾斜=false, 字号=13sp
📝 基本格式已应用: 内容='默认文字', 加粗=false, 倾斜=false, 字号=13sp
📝 formatData中无窗色信息，保持当前窗色设置
📝 完整格式数据应用完成: text_00701e4b-cf92-48f0-a87b-ac71b890578e
📝 遥控端文字窗口同步完成: text_00701e4b-cf92-48f0-a87b-ac71b890578e
📝 遥控端文字窗口数据同步完成: 成功=2, 失败=0
📝 文字内容可视化显示更新完成
【窗口容器View】🎨 跳过所有背景填充绘制，保持透明: 423af262
【窗口容器View】📝 文字窗口由TextView控件显示: 423af262

接收端日志：[WS] 【WebSocket服务器】收到原始消息: {"connectionId":"remote_control_1756126267931","data":{"timestamp":1756126619105,"request_type":"get_window_info"},"type":"window_manager_request"}
[WS] 【WebSocket服务器】来自客户端: /192.168.8.106:41308
[WS] 【WebSocket服务器】解析消息成功，类型: window_manager_request
[WS] 【WebSocket服务器】收到控制消息: window_manager_request, 连接ID: remote_control_1756126267931
【远程控制服务器】收到消息: window_manager_request
【远程控制服务器】处理消息: window_manager_request, 连接ID: remote_control_1756126267931
【远程控制服务器】找到活跃连接，处理消息: window_manager_request
【远程控制服务器】处理投屏窗口管理请求
【窗口管理】开始获取活跃窗口信息
【窗口管理】当前窗口数量: 2
【窗口管理】当前连接IDs: [text_c3428204-bd40-4afe-a58a-1c42423af262, text_00701e4b-cf92-48f0-a87b-ac71b890578e]
【窗口管理】StateManager中的连接数量: 0
【窗口管理】处理投屏窗口: text_c3428204-bd40-4afe-a58a-1c42423af262 (层级索引: 0)
【文本格式管理器】未找到保存的格式: ID=text_c3428204-bd40-4afe-a58a-1c42423af262
🎯 [容器位置] 容器X坐标: 409.6
🎯 [容器位置] 容器Y坐标: 746.2
【编辑状态】获取编辑状态: text_c3428204-bd40-4afe-a58a-1c42423af262, 内存状态: false
【编辑状态】返回内存状态: text_c3428204-bd40-4afe-a58a-1c42423af262 -> false
【窗口信息模块】获取编辑状态: text_c3428204-bd40-4afe-a58a-1c42423af262 -> false
【窗口信息模块】获取文本窗口背景颜色: 启用=false, 颜色=#FFFFFFFF
【窗口管理】添加文本窗口信息: 文本内容 (层级索引: 0)
【窗口管理】处理投屏窗口: text_00701e4b-cf92-48f0-a87b-ac71b890578e (层级索引: 1)
【文本格式管理器】未找到保存的格式: ID=text_00701e4b-cf92-48f0-a87b-ac71b890578e
🎯 [容器位置] 容器X坐标: 0.0
🎯 [容器位置] 容器Y坐标: 0.0
【编辑状态】获取编辑状态: text_00701e4b-cf92-48f0-a87b-ac71b890578e, 内存状态: null
【编辑状态】从SharedPreferences加载: text_00701e4b-cf92-48f0-a87b-ac71b890578e -> false
【编辑状态】返回持久化状态: text_00701e4b-cf92-48f0-a87b-ac71b890578e -> false
【窗口信息模块】获取编辑状态: text_00701e4b-cf92-48f0-a87b-ac71b890578e -> false
【窗口信息模块】获取文本窗口背景颜色: 启用=false, 颜色=#FFFFFFFF
【窗口管理】添加文本窗口信息: 文本内容 (层级索引: 1)
【窗口管理】最终获取 2 个窗口信息，已按实际层级排序
【窗口管理】显示位置 0 -> 层级 1: 本地设备:0 - 文本内容
【窗口管理】显示位置 1 -> 层级 2: 本地设备:0 - 文本内容
【远程控制服务器】获取到 2 个投屏窗口信息
【窗口管理】窗口 text_00701e4b-cf92-48f0-a87b-ac71b890578e 尺寸计算: 原始=300×200, 裁剪=false, 实际显示=300×200
【窗口管理】窗口 text_c3428204-bd40-4afe-a58a-1c42423af262 尺寸计算: 原始=300×200, 裁剪=false, 实际显示=300×200
[WS] 广播消息到 remote_control_1756126267931: window_manager_response
广播消息完成: window_manager_response, 成功: 1/1
【远程控制服务器】投屏窗口管理响应已发送，窗口数量: 2
[WS] 【WebSocket服务器】收到原始消息: {"connectionId":"remote_control_1756126267931","data":{"timestamp":1756126619334},"type":"text_content_request"}
[WS] 【WebSocket服务器】来自客户端: /192.168.8.106:41308
[WS] 【WebSocket服务器】解析消息成功，类型: text_content_request
[WS] 【WebSocket服务器】收到控制消息: text_content_request, 连接ID: remote_control_1756126267931
【远程控制服务器】收到消息: text_content_request
【远程控制服务器】处理消息: text_content_request, 连接ID: remote_control_1756126267931
【远程控制服务器】找到活跃连接，处理消息: text_content_request
📝 处理文字内容请求: remote_control_1756126267931
【文本格式管理器】未找到富文本格式: ID=text_c3428204-bd40-4afe-a58a-1c42423af262
【文本格式管理器】未找到保存的扩展格式: ID=text_c3428204-bd40-4afe-a58a-1c42423af262
【文本窗口管理器】窗口背景颜色信息已添加: ID=text_c3428204-bd40-4afe-a58a-1c42423af262, 启用=false, 颜色=#FFFFFFFF
【文本窗口管理器】完整格式信息已获取: ID=text_c3428204-bd40-4afe-a58a-1c42423af262, 包含5个字段
📝 获取文字窗口完整信息: text_c3428204-bd40-4afe-a58a-1c42423af262, 内容: 默认文字, 富文本: false
【文本格式管理器】未找到富文本格式: ID=text_00701e4b-cf92-48f0-a87b-ac71b890578e
【文本格式管理器】未找到保存的扩展格式: ID=text_00701e4b-cf92-48f0-a87b-ac71b890578e
【文本窗口管理器】窗口背景颜色信息已添加: ID=text_00701e4b-cf92-48f0-a87b-ac71b890578e, 启用=false, 颜色=#FFFFFFFF
【文本窗口管理器】完整格式信息已获取: ID=text_00701e4b-cf92-48f0-a87b-ac71b890578e, 包含5个字段
📝 获取文字窗口完整信息: text_00701e4b-cf92-48f0-a87b-ac71b890578e, 内容: 默认文字, 富文本: false
📝 文字内容获取完成，共 2 个文字窗口
[WS] 广播消息到 remote_control_1756126267931: text_content_response
广播消息完成: text_content_response, 成功: 1/1
📝 文字内容响应已发送，文字窗口数量: 2
📝 文字内容请求处理完成: remote_control_1756126267931, 文字窗口数量: 2
