package com.example.castapp.model;

/**
 * 🎯 统一的窗口参数管理类
 * 用于在遥控端统一管理所有窗口参数，避免分散存储和复杂收集
 */
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000b\n\u0002\b\n\n\u0002\u0010\u0007\n\u0002\b\u001b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\bp\n\u0002\u0010$\n\u0002\b\u0003\b\u0086\b\u0018\u0000 \u00a5\u00012\u00020\u0001:\u0002\u00a5\u0001B\u0085\u0003\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0005\u001a\u00020\u0006\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u000b\u001a\u00020\u0003\u0012\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\r\u001a\u00020\b\u0012\b\b\u0002\u0010\u000e\u001a\u00020\b\u0012\b\b\u0002\u0010\u000f\u001a\u00020\u0006\u0012\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u0003\u0012\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u0003\u0012\b\b\u0002\u0010\u0012\u001a\u00020\u0013\u0012\b\b\u0002\u0010\u0014\u001a\u00020\u0006\u0012\b\b\u0002\u0010\u0015\u001a\u00020\b\u0012\b\b\u0002\u0010\u0016\u001a\u00020\u0006\u0012\u0006\u0010\u0017\u001a\u00020\u0013\u0012\u0006\u0010\u0018\u001a\u00020\u0013\u0012\u0006\u0010\u0019\u001a\u00020\u0013\u0012\u0006\u0010\u001a\u001a\u00020\u0013\u0012\u0006\u0010\u001b\u001a\u00020\u0006\u0012\u0006\u0010\u001c\u001a\u00020\b\u0012\u0006\u0010\u001d\u001a\u00020\b\u0012\u0006\u0010\u001e\u001a\u00020\b\u0012\u0006\u0010\u001f\u001a\u00020\b\u0012\u0006\u0010 \u001a\u00020\b\u0012\u0006\u0010!\u001a\u00020\b\u0012\u0006\u0010\"\u001a\u00020\u0013\u0012\u0006\u0010#\u001a\u00020\u0013\u0012\u0006\u0010$\u001a\u00020\b\u0012\u0006\u0010%\u001a\u00020\u0006\u0012\u0006\u0010&\u001a\u00020\u0013\u0012\u0006\u0010\'\u001a\u00020\b\u0012\u0006\u0010(\u001a\u00020\b\u0012\u0006\u0010)\u001a\u00020\u0006\u0012\u0006\u0010*\u001a\u00020\u0006\u0012\u0006\u0010+\u001a\u00020\b\u0012\u0006\u0010,\u001a\u00020\u0006\u0012\u0006\u0010-\u001a\u00020\b\u0012\n\b\u0002\u0010.\u001a\u0004\u0018\u00010/\u0012\b\b\u0002\u00100\u001a\u00020\u0003\u0012\b\b\u0002\u00101\u001a\u000202\u00a2\u0006\u0002\u00103J\t\u0010s\u001a\u00020\u0003H\u00c6\u0003J\t\u0010t\u001a\u00020\bH\u00c6\u0003J\t\u0010u\u001a\u00020\u0006H\u00c6\u0003J\u000b\u0010v\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\u000b\u0010w\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\t\u0010x\u001a\u00020\u0013H\u00c6\u0003J\t\u0010y\u001a\u00020\u0006H\u00c6\u0003J\t\u0010z\u001a\u00020\bH\u00c6\u0003J\t\u0010{\u001a\u00020\u0006H\u00c6\u0003J\t\u0010|\u001a\u00020\u0013H\u00c6\u0003J\t\u0010}\u001a\u00020\u0013H\u00c6\u0003J\t\u0010~\u001a\u00020\u0003H\u00c6\u0003J\t\u0010\u007f\u001a\u00020\u0013H\u00c6\u0003J\n\u0010\u0080\u0001\u001a\u00020\u0013H\u00c6\u0003J\n\u0010\u0081\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u0082\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0083\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0084\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0085\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0086\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0087\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0088\u0001\u001a\u00020\u0013H\u00c6\u0003J\n\u0010\u0089\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u008a\u0001\u001a\u00020\u0013H\u00c6\u0003J\n\u0010\u008b\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u008c\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u008d\u0001\u001a\u00020\u0013H\u00c6\u0003J\n\u0010\u008e\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u008f\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0090\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u0091\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u0092\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0093\u0001\u001a\u00020\u0006H\u00c6\u0003J\n\u0010\u0094\u0001\u001a\u00020\bH\u00c6\u0003J\n\u0010\u0095\u0001\u001a\u00020\bH\u00c6\u0003J\f\u0010\u0096\u0001\u001a\u0004\u0018\u00010/H\u00c6\u0003J\n\u0010\u0097\u0001\u001a\u00020\u0003H\u00c6\u0003J\n\u0010\u0098\u0001\u001a\u000202H\u00c6\u0003J\f\u0010\u0099\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\f\u0010\u009a\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\n\u0010\u009b\u0001\u001a\u00020\u0003H\u00c6\u0003J\f\u0010\u009c\u0001\u001a\u0004\u0018\u00010\u0003H\u00c6\u0003J\n\u0010\u009d\u0001\u001a\u00020\bH\u00c6\u0003J\u00c4\u0003\u0010\u009e\u0001\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00032\b\b\u0002\u0010\u0005\u001a\u00020\u00062\b\b\u0002\u0010\u0007\u001a\u00020\b2\n\b\u0002\u0010\t\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\n\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u000b\u001a\u00020\u00032\n\b\u0002\u0010\f\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\r\u001a\u00020\b2\b\b\u0002\u0010\u000e\u001a\u00020\b2\b\b\u0002\u0010\u000f\u001a\u00020\u00062\n\b\u0002\u0010\u0010\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0011\u001a\u0004\u0018\u00010\u00032\b\b\u0002\u0010\u0012\u001a\u00020\u00132\b\b\u0002\u0010\u0014\u001a\u00020\u00062\b\b\u0002\u0010\u0015\u001a\u00020\b2\b\b\u0002\u0010\u0016\u001a\u00020\u00062\b\b\u0002\u0010\u0017\u001a\u00020\u00132\b\b\u0002\u0010\u0018\u001a\u00020\u00132\b\b\u0002\u0010\u0019\u001a\u00020\u00132\b\b\u0002\u0010\u001a\u001a\u00020\u00132\b\b\u0002\u0010\u001b\u001a\u00020\u00062\b\b\u0002\u0010\u001c\u001a\u00020\b2\b\b\u0002\u0010\u001d\u001a\u00020\b2\b\b\u0002\u0010\u001e\u001a\u00020\b2\b\b\u0002\u0010\u001f\u001a\u00020\b2\b\b\u0002\u0010 \u001a\u00020\b2\b\b\u0002\u0010!\u001a\u00020\b2\b\b\u0002\u0010\"\u001a\u00020\u00132\b\b\u0002\u0010#\u001a\u00020\u00132\b\b\u0002\u0010$\u001a\u00020\b2\b\b\u0002\u0010%\u001a\u00020\u00062\b\b\u0002\u0010&\u001a\u00020\u00132\b\b\u0002\u0010\'\u001a\u00020\b2\b\b\u0002\u0010(\u001a\u00020\b2\b\b\u0002\u0010)\u001a\u00020\u00062\b\b\u0002\u0010*\u001a\u00020\u00062\b\b\u0002\u0010+\u001a\u00020\b2\b\b\u0002\u0010,\u001a\u00020\u00062\b\b\u0002\u0010-\u001a\u00020\b2\n\b\u0002\u0010.\u001a\u0004\u0018\u00010/2\b\b\u0002\u00100\u001a\u00020\u00032\b\b\u0002\u00101\u001a\u000202H\u00c6\u0001J\u0015\u0010\u009f\u0001\u001a\u00020\b2\t\u0010\u00a0\u0001\u001a\u0004\u0018\u00010\u0001H\u00d6\u0003J\n\u0010\u00a1\u0001\u001a\u00020\u0006H\u00d6\u0001J\u0014\u0010\u00a2\u0001\u001a\u000f\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00a3\u0001J\n\u0010\u00a4\u0001\u001a\u00020\u0003H\u00d6\u0001R\u001a\u0010#\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b4\u00105\"\u0004\b6\u00107R\u001a\u0010*\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b8\u00109\"\u0004\b:\u0010;R\u001a\u0010)\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b<\u00109\"\u0004\b=\u0010;R\u001a\u0010%\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b>\u00109\"\u0004\b?\u0010;R\u001a\u0010&\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b@\u00105\"\u0004\bA\u00107R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bB\u0010CR\u001a\u0010\"\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bD\u00105\"\u0004\bE\u00107R\u0013\u0010.\u001a\u0004\u0018\u00010/\u00a2\u0006\b\n\u0000\u001a\u0004\bF\u0010GR\u0011\u00100\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bH\u0010CR\u0013\u0010\t\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bI\u0010CR\u001c\u0010\u0011\u001a\u0004\u0018\u00010\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bJ\u0010C\"\u0004\bK\u0010LR\u001c\u0010\u0010\u001a\u0004\u0018\u00010\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bM\u0010C\"\u0004\bN\u0010LR\u001a\u0010\u000f\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bO\u00109\"\u0004\bP\u0010;R\u0011\u0010\u0004\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\bQ\u0010CR\u0011\u0010\u0007\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0007\u0010RR\u001a\u0010\r\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\r\u0010R\"\u0004\bS\u0010TR\u001a\u0010$\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b$\u0010R\"\u0004\bU\u0010TR\u0011\u0010\'\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\'\u0010RR\u0011\u0010\u001c\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010RR\u0011\u0010\u001d\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001d\u0010RR\u0011\u0010(\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b(\u0010RR\u001a\u0010\u000e\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u000e\u0010R\"\u0004\bV\u0010TR\u0011\u0010-\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b-\u0010RR\u001a\u0010!\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b!\u0010R\"\u0004\bW\u0010TR\u0011\u0010\u001f\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001f\u0010RR\u0011\u0010\u001e\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010RR\u001a\u0010 \u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b \u0010R\"\u0004\bX\u0010TR\u001a\u0010\u0015\u001a\u00020\bX\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\u0015\u0010R\"\u0004\bY\u0010TR\u0011\u00101\u001a\u000202\u00a2\u0006\b\n\u0000\u001a\u0004\bZ\u0010[R\u001a\u0010\u0012\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b\\\u00105\"\u0004\b]\u00107R\u0013\u0010\n\u001a\u0004\u0018\u00010\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b^\u0010CR\u0011\u0010\u0005\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\b_\u00109R\u001a\u0010\u0017\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\b`\u00105\"\u0004\ba\u00107R\u001a\u0010\u0018\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bb\u00105\"\u0004\bc\u00107R\u001c\u0010\f\u001a\u0004\u0018\u00010\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bd\u0010C\"\u0004\be\u0010LR\u001a\u0010\u001a\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bf\u00105\"\u0004\bg\u00107R\u001a\u0010\u0019\u001a\u00020\u0013X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bh\u00105\"\u0004\bi\u00107R\u001a\u0010\u0014\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bj\u00109\"\u0004\bk\u0010;R\u001a\u0010\u000b\u001a\u00020\u0003X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bl\u0010C\"\u0004\bm\u0010LR\u0011\u0010,\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\bn\u00109R\u0011\u0010+\u001a\u00020\b\u00a2\u0006\b\n\u0000\u001a\u0004\bo\u0010RR\u001a\u0010\u0016\u001a\u00020\u0006X\u0086\u000e\u00a2\u0006\u000e\n\u0000\u001a\u0004\bp\u00109\"\u0004\bq\u0010;R\u0011\u0010\u001b\u001a\u00020\u0006\u00a2\u0006\b\n\u0000\u001a\u0004\br\u00109\u00a8\u0006\u00a6\u0001"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfig;", "", "connectionId", "", "ipAddress", "port", "", "isActive", "", "deviceName", "note", "textContent", "richTextData", "isBold", "isItalic", "fontSize", "fontName", "fontFamily", "lineSpacing", "", "textAlignment", "isWindowTextColorEnabled", "windowTextBackgroundColor", "positionX", "positionY", "scaleFactor", "rotationAngle", "zOrder", "isCropping", "isDragEnabled", "isScaleEnabled", "isRotationEnabled", "isVisible", "isMirrored", "cornerRadius", "alpha", "isBorderEnabled", "borderColor", "borderWidth", "isControlEnabled", "isEditEnabled", "baseWindowWidth", "baseWindowHeight", "windowColorEnabled", "windowBackgroundColor", "isLandscapeModeEnabled", "cropRectRatio", "Landroid/graphics/RectF;", "dataSource", "lastUpdated", "", "(Ljava/lang/String;Ljava/lang/String;IZLjava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZILjava/lang/String;Ljava/lang/String;FIZIFFFFIZZZZZZFFZIFZZIIZIZLandroid/graphics/RectF;Ljava/lang/String;J)V", "getAlpha", "()F", "setAlpha", "(F)V", "getBaseWindowHeight", "()I", "setBaseWindowHeight", "(I)V", "getBaseWindowWidth", "setBaseWindowWidth", "getBorderColor", "setBorderColor", "getBorderWidth", "setBorderWidth", "getConnectionId", "()Ljava/lang/String;", "getCornerRadius", "setCornerRadius", "getCropRectRatio", "()Landroid/graphics/RectF;", "getDataSource", "getDeviceName", "getFontFamily", "setFontFamily", "(Ljava/lang/String;)V", "getFontName", "setFontName", "getFontSize", "setFontSize", "getIpAddress", "()Z", "setBold", "(Z)V", "setBorderEnabled", "setItalic", "setMirrored", "setVisible", "setWindowTextColorEnabled", "getLastUpdated", "()J", "getLineSpacing", "setLineSpacing", "getNote", "getPort", "getPositionX", "setPositionX", "getPositionY", "setPositionY", "getRichTextData", "setRichTextData", "getRotationAngle", "setRotationAngle", "getScaleFactor", "setScaleFactor", "getTextAlignment", "setTextAlignment", "getTextContent", "setTextContent", "getWindowBackgroundColor", "getWindowColorEnabled", "getWindowTextBackgroundColor", "setWindowTextBackgroundColor", "getZOrder", "component1", "component10", "component11", "component12", "component13", "component14", "component15", "component16", "component17", "component18", "component19", "component2", "component20", "component21", "component22", "component23", "component24", "component25", "component26", "component27", "component28", "component29", "component3", "component30", "component31", "component32", "component33", "component34", "component35", "component36", "component37", "component38", "component39", "component4", "component40", "component41", "component42", "component43", "component5", "component6", "component7", "component8", "component9", "copy", "equals", "other", "hashCode", "toBatchSyncData", "", "toString", "Companion", "app_debug"})
public final class RemoteWindowConfig {
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String connectionId = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String ipAddress = null;
    private final int port = 0;
    private final boolean isActive = false;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String deviceName = null;
    @org.jetbrains.annotations.Nullable()
    private final java.lang.String note = null;
    @org.jetbrains.annotations.NotNull()
    private java.lang.String textContent;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String richTextData;
    private boolean isBold;
    private boolean isItalic;
    private int fontSize;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String fontName;
    @org.jetbrains.annotations.Nullable()
    private java.lang.String fontFamily;
    private float lineSpacing;
    private int textAlignment;
    private boolean isWindowTextColorEnabled;
    private int windowTextBackgroundColor;
    private float positionX;
    private float positionY;
    private float scaleFactor;
    private float rotationAngle;
    private final int zOrder = 0;
    private final boolean isCropping = false;
    private final boolean isDragEnabled = false;
    private final boolean isScaleEnabled = false;
    private final boolean isRotationEnabled = false;
    private boolean isVisible;
    private boolean isMirrored;
    private float cornerRadius;
    private float alpha;
    private boolean isBorderEnabled;
    private int borderColor;
    private float borderWidth;
    private final boolean isControlEnabled = false;
    private final boolean isEditEnabled = false;
    private int baseWindowWidth;
    private int baseWindowHeight;
    private final boolean windowColorEnabled = false;
    private final int windowBackgroundColor = 0;
    private final boolean isLandscapeModeEnabled = false;
    @org.jetbrains.annotations.Nullable()
    private final android.graphics.RectF cropRectRatio = null;
    @org.jetbrains.annotations.NotNull()
    private final java.lang.String dataSource = null;
    private final long lastUpdated = 0L;
    @org.jetbrains.annotations.NotNull()
    public static final com.example.castapp.model.RemoteWindowConfig.Companion Companion = null;
    
    public RemoteWindowConfig(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, boolean isActive, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.Nullable()
    java.lang.String note, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent, @org.jetbrains.annotations.Nullable()
    java.lang.String richTextData, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String fontName, @org.jetbrains.annotations.Nullable()
    java.lang.String fontFamily, float lineSpacing, int textAlignment, boolean isWindowTextColorEnabled, int windowTextBackgroundColor, float positionX, float positionY, float scaleFactor, float rotationAngle, int zOrder, boolean isCropping, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isVisible, boolean isMirrored, float cornerRadius, float alpha, boolean isBorderEnabled, int borderColor, float borderWidth, boolean isControlEnabled, boolean isEditEnabled, int baseWindowWidth, int baseWindowHeight, boolean windowColorEnabled, int windowBackgroundColor, boolean isLandscapeModeEnabled, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, @org.jetbrains.annotations.NotNull()
    java.lang.String dataSource, long lastUpdated) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getConnectionId() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getIpAddress() {
        return null;
    }
    
    public final int getPort() {
        return 0;
    }
    
    public final boolean isActive() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getDeviceName() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getNote() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getTextContent() {
        return null;
    }
    
    public final void setTextContent(@org.jetbrains.annotations.NotNull()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getRichTextData() {
        return null;
    }
    
    public final void setRichTextData(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    public final boolean isBold() {
        return false;
    }
    
    public final void setBold(boolean p0) {
    }
    
    public final boolean isItalic() {
        return false;
    }
    
    public final void setItalic(boolean p0) {
    }
    
    public final int getFontSize() {
        return 0;
    }
    
    public final void setFontSize(int p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFontName() {
        return null;
    }
    
    public final void setFontName(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String getFontFamily() {
        return null;
    }
    
    public final void setFontFamily(@org.jetbrains.annotations.Nullable()
    java.lang.String p0) {
    }
    
    public final float getLineSpacing() {
        return 0.0F;
    }
    
    public final void setLineSpacing(float p0) {
    }
    
    public final int getTextAlignment() {
        return 0;
    }
    
    public final void setTextAlignment(int p0) {
    }
    
    public final boolean isWindowTextColorEnabled() {
        return false;
    }
    
    public final void setWindowTextColorEnabled(boolean p0) {
    }
    
    public final int getWindowTextBackgroundColor() {
        return 0;
    }
    
    public final void setWindowTextBackgroundColor(int p0) {
    }
    
    public final float getPositionX() {
        return 0.0F;
    }
    
    public final void setPositionX(float p0) {
    }
    
    public final float getPositionY() {
        return 0.0F;
    }
    
    public final void setPositionY(float p0) {
    }
    
    public final float getScaleFactor() {
        return 0.0F;
    }
    
    public final void setScaleFactor(float p0) {
    }
    
    public final float getRotationAngle() {
        return 0.0F;
    }
    
    public final void setRotationAngle(float p0) {
    }
    
    public final int getZOrder() {
        return 0;
    }
    
    public final boolean isCropping() {
        return false;
    }
    
    public final boolean isDragEnabled() {
        return false;
    }
    
    public final boolean isScaleEnabled() {
        return false;
    }
    
    public final boolean isRotationEnabled() {
        return false;
    }
    
    public final boolean isVisible() {
        return false;
    }
    
    public final void setVisible(boolean p0) {
    }
    
    public final boolean isMirrored() {
        return false;
    }
    
    public final void setMirrored(boolean p0) {
    }
    
    public final float getCornerRadius() {
        return 0.0F;
    }
    
    public final void setCornerRadius(float p0) {
    }
    
    public final float getAlpha() {
        return 0.0F;
    }
    
    public final void setAlpha(float p0) {
    }
    
    public final boolean isBorderEnabled() {
        return false;
    }
    
    public final void setBorderEnabled(boolean p0) {
    }
    
    public final int getBorderColor() {
        return 0;
    }
    
    public final void setBorderColor(int p0) {
    }
    
    public final float getBorderWidth() {
        return 0.0F;
    }
    
    public final void setBorderWidth(float p0) {
    }
    
    public final boolean isControlEnabled() {
        return false;
    }
    
    public final boolean isEditEnabled() {
        return false;
    }
    
    public final int getBaseWindowWidth() {
        return 0;
    }
    
    public final void setBaseWindowWidth(int p0) {
    }
    
    public final int getBaseWindowHeight() {
        return 0;
    }
    
    public final void setBaseWindowHeight(int p0) {
    }
    
    public final boolean getWindowColorEnabled() {
        return false;
    }
    
    public final int getWindowBackgroundColor() {
        return 0;
    }
    
    public final boolean isLandscapeModeEnabled() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF getCropRectRatio() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String getDataSource() {
        return null;
    }
    
    public final long getLastUpdated() {
        return 0L;
    }
    
    /**
     * 🔄 转换为批量同步消息的数据格式
     */
    @org.jetbrains.annotations.NotNull()
    public final java.util.Map<java.lang.String, java.lang.Object> toBatchSyncData() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component1() {
        return null;
    }
    
    public final boolean component10() {
        return false;
    }
    
    public final int component11() {
        return 0;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component12() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component13() {
        return null;
    }
    
    public final float component14() {
        return 0.0F;
    }
    
    public final int component15() {
        return 0;
    }
    
    public final boolean component16() {
        return false;
    }
    
    public final int component17() {
        return 0;
    }
    
    public final float component18() {
        return 0.0F;
    }
    
    public final float component19() {
        return 0.0F;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component2() {
        return null;
    }
    
    public final float component20() {
        return 0.0F;
    }
    
    public final float component21() {
        return 0.0F;
    }
    
    public final int component22() {
        return 0;
    }
    
    public final boolean component23() {
        return false;
    }
    
    public final boolean component24() {
        return false;
    }
    
    public final boolean component25() {
        return false;
    }
    
    public final boolean component26() {
        return false;
    }
    
    public final boolean component27() {
        return false;
    }
    
    public final boolean component28() {
        return false;
    }
    
    public final float component29() {
        return 0.0F;
    }
    
    public final int component3() {
        return 0;
    }
    
    public final float component30() {
        return 0.0F;
    }
    
    public final boolean component31() {
        return false;
    }
    
    public final int component32() {
        return 0;
    }
    
    public final float component33() {
        return 0.0F;
    }
    
    public final boolean component34() {
        return false;
    }
    
    public final boolean component35() {
        return false;
    }
    
    public final int component36() {
        return 0;
    }
    
    public final int component37() {
        return 0;
    }
    
    public final boolean component38() {
        return false;
    }
    
    public final int component39() {
        return 0;
    }
    
    public final boolean component4() {
        return false;
    }
    
    public final boolean component40() {
        return false;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final android.graphics.RectF component41() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component42() {
        return null;
    }
    
    public final long component43() {
        return 0L;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component5() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component6() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final java.lang.String component7() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable()
    public final java.lang.String component8() {
        return null;
    }
    
    public final boolean component9() {
        return false;
    }
    
    @org.jetbrains.annotations.NotNull()
    public final com.example.castapp.model.RemoteWindowConfig copy(@org.jetbrains.annotations.NotNull()
    java.lang.String connectionId, @org.jetbrains.annotations.NotNull()
    java.lang.String ipAddress, int port, boolean isActive, @org.jetbrains.annotations.Nullable()
    java.lang.String deviceName, @org.jetbrains.annotations.Nullable()
    java.lang.String note, @org.jetbrains.annotations.NotNull()
    java.lang.String textContent, @org.jetbrains.annotations.Nullable()
    java.lang.String richTextData, boolean isBold, boolean isItalic, int fontSize, @org.jetbrains.annotations.Nullable()
    java.lang.String fontName, @org.jetbrains.annotations.Nullable()
    java.lang.String fontFamily, float lineSpacing, int textAlignment, boolean isWindowTextColorEnabled, int windowTextBackgroundColor, float positionX, float positionY, float scaleFactor, float rotationAngle, int zOrder, boolean isCropping, boolean isDragEnabled, boolean isScaleEnabled, boolean isRotationEnabled, boolean isVisible, boolean isMirrored, float cornerRadius, float alpha, boolean isBorderEnabled, int borderColor, float borderWidth, boolean isControlEnabled, boolean isEditEnabled, int baseWindowWidth, int baseWindowHeight, boolean windowColorEnabled, int windowBackgroundColor, boolean isLandscapeModeEnabled, @org.jetbrains.annotations.Nullable()
    android.graphics.RectF cropRectRatio, @org.jetbrains.annotations.NotNull()
    java.lang.String dataSource, long lastUpdated) {
        return null;
    }
    
    @java.lang.Override()
    public boolean equals(@org.jetbrains.annotations.Nullable()
    java.lang.Object other) {
        return false;
    }
    
    @java.lang.Override()
    public int hashCode() {
        return 0;
    }
    
    @java.lang.Override()
    @org.jetbrains.annotations.NotNull()
    public java.lang.String toString() {
        return null;
    }
    
    /**
     * 🔄 从CastWindowInfo创建统一参数
     */
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u001a\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00062\n\b\u0002\u0010\u0007\u001a\u0004\u0018\u00010\b\u00a8\u0006\t"}, d2 = {"Lcom/example/castapp/model/RemoteWindowConfig$Companion;", "", "()V", "fromCastWindowInfo", "Lcom/example/castapp/model/RemoteWindowConfig;", "windowInfo", "Lcom/example/castapp/model/CastWindowInfo;", "context", "Landroid/content/Context;", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull()
        public final com.example.castapp.model.RemoteWindowConfig fromCastWindowInfo(@org.jetbrains.annotations.NotNull()
        com.example.castapp.model.CastWindowInfo windowInfo, @org.jetbrains.annotations.Nullable()
        android.content.Context context) {
            return null;
        }
    }
}